<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/images/icon.png" />
    
    <!-- Import <PERSON> font -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Albert+Sans:ital,wght@0,100..900;1,100..900&display=swap" rel="stylesheet">


    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>LZ Transcription</title>
    <!-- Google tag (gtag.js) - Only loaded in production -->
    <script>
      // Check if we're in production mode
      if (window.location.hostname !== 'localhost' &&
          !window.location.hostname.includes('127.0.0.1') &&
          !window.location.hostname.includes('.local')) {
        // Create script element for Google Analytics
        const gaScript = document.createElement('script');
        gaScript.async = true;
        gaScript.src = 'https://www.googletagmanager.com/gtag/js?id=G-9WR8P9W5HB';
        document.head.appendChild(gaScript);

        // Initialize Google Analytics
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        // Initialize with consent mode
        gtag('consent', 'default', {
          'analytics_storage': 'denied'
        });
      } else {
        console.log('Google Analytics not loaded in development mode');
        // Create a mock gtag function for development
        window.gtag = function() {
          console.log('[DEV ONLY] gtag call:', arguments);
        };
      }
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
